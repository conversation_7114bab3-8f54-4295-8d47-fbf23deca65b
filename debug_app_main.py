#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 app_main 文件生成问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置临时环境变量
os.environ['LANGCHAIN_API_KEY'] = 'test-key-for-debug'
os.environ['LANGCHAIN_BASE_URL'] = 'https://test.example.com'

def debug_module_architect():
    """调试模块架构师的输出"""
    print("=== 调试模块架构师输出 ===")
    
    try:
        from app.langgraph_def.agent_state import AgentState
        from app.langgraph_def.graph_builder import module_architect_node
        
        # 创建测试状态
        test_state = AgentState(
            workflow_id="debug-123",
            user_id=1,
            project_name="调试项目",
            status="RUNNING",
            workflow_steps=[],
            user_input="测试输入",
            device_tasks_queue=[],
            system_plan=None,
            workspace_path="/tmp/test",
            available_actions=[],
            current_device_task={
                "device_role": "温湿度传感器",
                "description": "定期读取DHT22传感器的温度和湿度数据，并上报到涂鸦云平台。",
                "internal_device_id": "test-device-001",
                "board": "esp32dev",
                "peripherals": [
                    {
                        "name": "DHT22",
                        "model": "DHT22",
                        "pins": [{"name": "DATA", "number": 4}]
                    }
                ]
            },
            current_api_spec=None,
            module_tasks=[],
            current_module_task=None,
            completed_modules={},
            feedback="",
            project_files={},
            test_plan=None,
            original_module_plan=None,
            build_dir="",
            firmware_path=None,
            deployment_choice=None,
            dp_info_list=[],
            faulty_module=None,
            user_action=None,
            device_dp_contract=[],
            wifi_ssid="TestWiFi",
            wifi_password="password123",
            cloud_product_id="test_product",
            cloud_device_id="test_device",
            cloud_device_secret="test_secret"
        )
        
        print("✅ 测试状态创建成功")
        print(f"📋 设备角色: {test_state['current_device_task']['device_role']}")
        print(f"📋 外设数量: {len(test_state['current_device_task']['peripherals'])}")
        
        # 注意：这里不实际调用 module_architect_node，因为它需要API调用
        # 我们只是检查函数是否可以导入和调用
        print("✅ module_architect_node 函数导入成功")
        
        # 模拟预期的输出
        expected_modules = [
            {"task_id": "config_manager", "task_type": "driver", "peripheral": "Core"},
            {"task_id": "ota_handler", "task_type": "driver", "peripheral": "Core"},
            {"task_id": "mqtt_logger", "task_type": "driver", "peripheral": "Core"},
            {"task_id": "tuya_handler", "task_type": "driver", "peripheral": "Core"},
            {"task_id": "dht22_driver", "task_type": "driver", "peripheral": "DHT22"},
            {"task_id": "app_main", "task_type": "application", "description": "The main application logic."}
        ]
        
        print(f"📊 预期生成的模块数量: {len(expected_modules)}")
        for i, module in enumerate(expected_modules):
            print(f"  {i+1}. {module['task_id']} ({module['task_type']})")
        
        # 检查 app_main 是否在列表中
        app_main_module = next((m for m in expected_modules if m['task_id'] == 'app_main'), None)
        if app_main_module:
            print(f"✅ app_main 模块存在，类型: {app_main_module['task_type']}")
        else:
            print("❌ app_main 模块不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_developer_logic():
    """调试开发者节点的逻辑"""
    print("\n=== 调试开发者节点逻辑 ===")
    
    try:
        from app.langgraph_def.graph_builder import developer_node
        
        print("✅ developer_node 函数导入成功")
        
        # 模拟 app_main 任务
        app_main_task = {
            "task_id": "app_main",
            "task_type": "application",
            "description": "The main application logic.",
            "dependencies": ["config_manager", "ota_handler", "mqtt_logger", "tuya_handler", "dht22_driver"]
        }
        
        print(f"📋 模拟 app_main 任务:")
        print(f"  - task_id: {app_main_task['task_id']}")
        print(f"  - task_type: {app_main_task['task_type']}")
        print(f"  - dependencies: {len(app_main_task['dependencies'])} 个")
        
        # 检查任务类型判断逻辑
        if app_main_task['task_type'] == 'driver':
            print("❌ 错误：app_main 被识别为 driver 类型")
            return False
        else:
            print("✅ 正确：app_main 被识别为非 driver 类型，应该进入 application 分支")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_workflow_sequence():
    """调试工作流序列"""
    print("\n=== 调试工作流序列 ===")

    try:
        from app.langgraph_def.graph_builder import build_graph

        graph = build_graph()
        print("✅ 工作流图构建成功")

        # 检查关键节点的连接
        nodes = graph.nodes
        print(f"📊 总节点数: {len(nodes)}")

        # 检查关键路径
        key_nodes = ["device_dispatcher", "dp_designer", "device_artifact_generator",
                    "module_architect", "module_dispatcher", "developer"]

        for node in key_nodes:
            if node in nodes:
                print(f"✅ 节点 '{node}' 存在")
            else:
                print(f"❌ 节点 '{node}' 不存在")
                return False

        print("\n📋 预期的工作流序列:")
        print("1. device_dispatcher → dp_designer")
        print("2. dp_designer → device_artifact_generator")
        print("3. device_artifact_generator → module_architect")
        print("4. module_architect → module_dispatcher")
        print("5. module_dispatcher → api_designer (如果有模块)")
        print("6. api_designer → developer")
        print("7. developer → module_dispatcher (循环)")
        print("8. module_dispatcher → integrator (如果没有更多模块)")

        return True

    except Exception as e:
        print(f"❌ 工作流序列调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始调试 app_main 文件生成问题...")

    success = True

    # 调试模块架构师
    if not debug_module_architect():
        success = False

    # 调试开发者节点逻辑
    if not debug_developer_logic():
        success = False

    # 调试工作流序列
    if not debug_workflow_sequence():
        success = False

    if success:
        print("\n🎉 调试完成，逻辑看起来正确！")
        print("\n💡 可能的问题原因:")
        print("1. LLM API 调用失败 - 检查网络连接和API密钥")
        print("2. 状态传递中断 - 检查节点间的状态更新")
        print("3. 模块任务生成失败 - 检查 module_architect_node 的输出")
        print("4. 契约数据丢失 - 检查 device_dp_contract 的传递")
        print("\n🔧 建议的调试步骤:")
        print("1. 检查日志输出，看哪个节点没有执行")
        print("2. 在 module_dispatcher_node 中添加调试输出")
        print("3. 在 developer_node 中添加调试输出")
        print("4. 检查 module_architect_node 是否成功生成了 app_main 任务")
    else:
        print("\n❌ 发现逻辑问题，需要进一步检查。")
        sys.exit(1)
