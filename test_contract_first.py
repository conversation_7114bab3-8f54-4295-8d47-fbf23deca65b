#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试契约先行重构的简单验证脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.langgraph_def.agent_state import Agent<PERSON>tate
from app.langgraph_def.graph_builder import dp_designer_node

def test_dp_designer_node():
    """测试新的DP设计节点"""
    print("=== 测试DP设计节点 ===")
    
    # 创建测试状态
    test_state = AgentState(
        workflow_id="test-123",
        user_id=1,
        project_name="测试项目",
        status="RUNNING",
        workflow_steps=[],
        user_input="测试输入",
        device_tasks_queue=[],
        system_plan=None,
        workspace_path="/tmp/test",
        available_actions=[],
        current_device_task={
            "device_role": "温湿度传感器",
            "description": "定期读取DHT22传感器的温度和湿度数据，并上报到涂鸦云平台。同时支持远程控制传感器的采样频率。",
            "internal_device_id": "test-device-001"
        },
        current_api_spec=None,
        module_tasks=[],
        current_module_task=None,
        completed_modules={},
        feedback="",
        project_files={},
        test_plan=None,
        original_module_plan=None,
        build_dir="",
        firmware_path=None,
        deployment_choice=None,
        dp_info_list=[],
        faulty_module=None,
        user_action=None,
        device_dp_contract=[],
        wifi_ssid="TestWiFi",
        wifi_password="password123",
        cloud_product_id="test_product",
        cloud_device_id="test_device",
        cloud_device_secret="test_secret"
    )
    
    try:
        # 调用DP设计节点
        result = dp_designer_node(test_state)
        
        print(f"✅ DP设计节点执行成功")
        print(f"📋 返回的契约字段: {list(result.keys())}")
        
        if "device_dp_contract" in result:
            contract = result["device_dp_contract"]
            print(f"📊 设计的DP数量: {len(contract)}")
            
            if contract:
                print("📝 设计的DP示例:")
                for i, dp in enumerate(contract[:2]):  # 只显示前2个
                    print(f"  {i+1}. {dp.get('name', 'Unknown')} ({dp.get('code', 'unknown')})")
                    print(f"     类型: {dp.get('type', 'unknown')}, 模式: {dp.get('mode', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ DP设计节点执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_structure():
    """测试工作流结构"""
    print("\n=== 测试工作流结构 ===")
    
    try:
        from app.langgraph_def.graph_builder import build_graph
        
        # 构建图
        graph = build_graph()
        print("✅ 工作流图构建成功")
        
        # 检查节点是否存在
        nodes = graph.nodes
        expected_nodes = ["dp_designer", "device_artifact_generator"]
        
        for node in expected_nodes:
            if node in nodes:
                print(f"✅ 节点 '{node}' 存在")
            else:
                print(f"❌ 节点 '{node}' 不存在")
                return False
        
        # 检查是否移除了旧节点
        if "dp_extractor" in nodes:
            print("❌ 旧节点 'dp_extractor' 仍然存在")
            return False
        else:
            print("✅ 旧节点 'dp_extractor' 已成功移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试契约先行重构...")
    
    success = True
    
    # 测试DP设计节点
    if not test_dp_designer_node():
        success = False
    
    # 测试工作流结构
    if not test_workflow_structure():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！契约先行重构成功完成。")
        print("\n📋 重构总结:")
        print("1. ✅ dp_extractor_node -> dp_designer_node (角色转换)")
        print("2. ✅ developer_node 支持契约注入")
        print("3. ✅ 工作流程序调整为契约先行")
        print("4. ✅ 新增 device_dp_contract 状态字段")
    else:
        print("\n❌ 部分测试失败，请检查重构实现。")
        sys.exit(1)
